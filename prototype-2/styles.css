/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    background: white;
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.top-bar {
    background: #2c5aa0;
    color: white;
    padding: 8px 0;
    font-size: 0.9rem;
}

.top-bar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.contact-info {
    display: flex;
    gap: 2rem;
}

.contact-info span {
    display: flex;
    align-items: center;
    gap: 8px;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    color: white;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.social-links a:hover {
    color: #ffd700;
}

.navbar {
    background: white;
    padding: 1rem 0;
}

.nav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo img {
    height: 50px;
    width: auto;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: #333;
    font-weight: 500;
    padding: 8px 16px;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link:hover {
    color: #2c5aa0;
}

.contact-btn {
    background: linear-gradient(135deg, #2c5aa0, #1e3a8a);
    color: white !important;
    padding: 12px 24px;
}

.contact-btn:hover {
    background: linear-gradient(135deg, #1e3a8a, #2c5aa0);
    transform: translateY(-2px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: #333;
    margin: 3px 0;
    transition: 0.3s;
}

/* Hero Section */
.hero {
    height: 100vh;
    position: relative;
    overflow: hidden;
}

.hero-slider {
    position: relative;
    height: 100%;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: opacity 1s ease-in-out;
}

.slide.active {
    opacity: 1;
}

.slide-bg {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.slide-content {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    transform: translateY(-50%);
    text-align: center;
    color: white;
    z-index: 2;
}

.slide-content h1 {
    font-size: 3.5rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.slide-content p {
    font-size: 1.2rem;
    margin-bottom: 2rem;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.cta-button {
    display: inline-block;
    background: linear-gradient(135deg, #ffd700, #ffb700);
    color: #333;
    padding: 16px 32px;
    border-radius: 50px;
    text-decoration: none;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.cta-button:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.slider-controls {
    position: absolute;
    top: 50%;
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 0 2rem;
    z-index: 3;
}

.slider-controls button {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.slider-controls button:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.slider-dots {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 1rem;
    z-index: 3;
}

.dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.5);
    cursor: pointer;
    transition: all 0.3s ease;
}

.dot.active {
    background: #ffd700;
    transform: scale(1.2);
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-header h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: #666;
    max-width: 600px;
    margin: 0 auto;
}

/* About Section */
.about {
    background: #f8fafc;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.lead {
    font-size: 1.2rem;
    color: #ffd700;
    font-weight: 600;
    margin-bottom: 1.5rem;
}

.about-text p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.8;
}

.values-grid {
    display: grid;
    gap: 1.5rem;
}

.value-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.value-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #2c5aa0, #1e3a8a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.value-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c5aa0;
    margin-bottom: 0.5rem;
}

.value-content p {
    color: #666;
    font-size: 0.95rem;
    line-height: 1.6;
}

.about-image {
    position: relative;
}

.about-image img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.experience-badge {
    position: absolute;
    bottom: -20px;
    right: -20px;
    background: linear-gradient(135deg, #ffd700, #ffb700);
    color: #333;
    padding: 1.5rem;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(255, 215, 0, 0.3);
}

.experience-badge .years {
    display: block;
    font-size: 2rem;
    font-weight: 800;
    line-height: 1;
}

.experience-badge .text {
    font-size: 0.9rem;
    font-weight: 600;
}

/* Products Section */
.products-tabs {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.tab-btn {
    background: transparent;
    border: 2px solid #2c5aa0;
    color: #2c5aa0;
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
}

.tab-btn.active,
.tab-btn:hover {
    background: #2c5aa0;
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.products-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.product-card {
    background: white;
    padding: 2rem;
    border-radius: 20px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    border: 1px solid #f0f0f0;
}

.product-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.product-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #ffd700, #ffb700);
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #333;
    font-size: 1.8rem;
    margin-bottom: 1.5rem;
}

.product-card h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.product-card p {
    color: #666;
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.product-card ul {
    list-style: none;
    margin: 0;
    padding: 0;
}

.product-card ul li {
    color: #666;
    margin-bottom: 0.5rem;
    position: relative;
    padding-left: 1.5rem;
}

.product-card ul li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: #ffd700;
    font-weight: bold;
}

/* Experience Section */
.experience {
    background: linear-gradient(135deg, #2c5aa0, #1e3a8a);
    color: white;
}

.experience .section-header h2 {
    color: white;
}

.experience .section-header p {
    color: rgba(255, 255, 255, 0.8);
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.1);
    padding: 2rem;
    border-radius: 20px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.15);
}

.stat-icon {
    width: 60px;
    height: 60px;
    background: #ffd700;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    color: #333;
    font-size: 1.5rem;
}

.stat-number {
    font-size: 3rem;
    font-weight: 800;
    color: #ffd700;
    margin-bottom: 0.5rem;
}

.stat-label {
    font-size: 1rem;
    opacity: 0.9;
    font-weight: 500;
}

/* News Section */
.news {
    background: #f8fafc;
}

.news-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr;
    gap: 2rem;
}

.news-card {
    background: white;
    border-radius: 20px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
}

.news-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.news-card.featured {
    grid-row: span 2;
}

.news-image {
    height: 200px;
    position: relative;
    overflow: hidden;
}

.news-card.featured .news-image {
    height: 300px;
}

.news-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.news-card:hover .news-image img {
    transform: scale(1.05);
}

.news-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #ffd700;
    color: #333;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
}

.news-content {
    padding: 1.5rem;
}

.news-meta {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;
}

.date {
    color: #666;
    font-size: 0.9rem;
}

.category {
    background: #2c5aa0;
    color: white;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
}

.news-card h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #2c5aa0;
    margin-bottom: 1rem;
    line-height: 1.4;
}

.news-card.featured h3 {
    font-size: 1.4rem;
}

.news-card p {
    color: #666;
    margin-bottom: 1rem;
    line-height: 1.6;
}

.read-more {
    color: #2c5aa0;
    text-decoration: none;
    font-weight: 600;
    display: inline-flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.read-more:hover {
    gap: 12px;
}

/* Contact Section */
.contact {
    background: white;
}

.contact-wrapper {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info h2 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #2c5aa0;
    margin-bottom: 1rem;
}

.contact-info > p {
    color: #666;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.info-cards {
    display: grid;
    gap: 1.5rem;
}

.info-card {
    display: flex;
    gap: 1rem;
    padding: 1.5rem;
    background: #f8fafc;
    border-radius: 15px;
    transition: all 0.3s ease;
}

.info-card:hover {
    background: #e2e8f0;
    transform: translateX(5px);
}

.info-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #2c5aa0, #1e3a8a);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.info-content h4 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c5aa0;
    margin-bottom: 0.5rem;
}

.info-content p {
    color: #666;
    line-height: 1.6;
}

.contact-form {
    background: #f8fafc;
    padding: 2.5rem;
    border-radius: 20px;
    border: 1px solid #e2e8f0;
}

.form-header {
    text-align: center;
    margin-bottom: 2rem;
}

.form-header h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c5aa0;
    margin-bottom: 0.5rem;
}

.form-header p {
    color: #666;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #333;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #e2e8f0;
    border-radius: 10px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: white;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #2c5aa0;
    box-shadow: 0 0 0 3px rgba(44, 90, 160, 0.1);
}

.submit-btn {
    width: 100%;
    background: linear-gradient(135deg, #2c5aa0, #1e3a8a);
    color: white;
    border: none;
    padding: 16px;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(44, 90, 160, 0.3);
}

/* Footer */
.footer {
    background: #1a202c;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
    margin-bottom: 2rem;
}

.footer-logo {
    height: 60px;
    margin-bottom: 1rem;
    filter: brightness(0) invert(1);
}

.company-info p {
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.footer-social a {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.1rem;
    transition: all 0.3s ease;
}

.footer-social a:hover {
    background: #ffd700;
    color: #333;
    transform: translateY(-3px);
}

.footer-section h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #ffd700;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: #ffd700;
}

.contact-details p {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
    display: flex;
    align-items: flex-start;
    gap: 10px;
}

.contact-details i {
    color: #ffd700;
    margin-top: 2px;
    flex-shrink: 0;
}

.store-badges {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.store-badges a {
    display: block;
    background: white;
    padding: 8px;
    border-radius: 8px;
    transition: transform 0.3s ease;
}

.store-badges a:hover {
    transform: translateY(-2px);
}

.store-badges img {
    height: 25px;
    width: auto;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 1rem;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

.footer-bottom-content a {
    color: #ffd700;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }

    .nav-menu {
        position: fixed;
        left: -100%;
        top: 100%;
        flex-direction: column;
        background-color: white;
        width: 100%;
        text-align: center;
        transition: 0.3s;
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
        gap: 1rem;
    }

    .nav-menu.active {
        left: 0;
    }

    .top-bar-content {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .contact-info {
        flex-direction: column;
        gap: 0.5rem;
    }

    .slide-content h1 {
        font-size: 2.5rem;
    }

    .slide-content p {
        font-size: 1rem;
        padding: 0 1rem;
    }

    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-image {
        order: -1;
    }

    .experience-badge {
        position: static;
        margin-top: 1rem;
        display: inline-block;
    }

    .products-tabs {
        flex-direction: column;
        align-items: center;
    }

    .tab-btn {
        width: 200px;
    }

    .products-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .news-grid {
        grid-template-columns: 1fr;
    }

    .contact-wrapper {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .footer-content {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .footer-bottom-content {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .store-badges {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .slider-controls {
        padding: 0 1rem;
    }

    .slider-controls button {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }
}
